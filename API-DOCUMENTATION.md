# WhatsApp API Documentation

This document provides detailed information about the WhatsApp API endpoints.

## API Endpoints

### Send Message

Send a message to a WhatsApp contact.

**Endpoint:** `POST /api/send`

**Request Body Parameters:**

| Parameter    | Type   | Required | Description                                                |
|--------------|--------|----------|------------------------------------------------------------|
| phone        | string | Yes      | Recipient's phone number (with or without country code)    |
| message      | string | *        | Text message to send                                       |
| mediaUrl     | string | *        | URL to an image to send                                    |
| documentUrl  | string | *        | URL to a document/PDF to send                              |
| documentName | string | No       | Name of the document file (defaults to 'document.pdf')     |
| mimetype     | string | No       | MIME type of the document (defaults to 'application/pdf')  |

\* At least one of `message`, `mediaUrl`, or `documentUrl` must be provided.

**Example Requests:**

1. Send a text message:
```json
{
  "phone": "60123456789",
  "message": "Hello from WhatsApp API!"
}
```

2. Send an image:
```json
{
  "phone": "60123456789",
  "mediaUrl": "https://example.com/image.jpg",
  "message": "Check out this image!"
}
```

3. Send a document/PDF:
```json
{
  "phone": "60123456789",
  "documentUrl": "https://example.com/document.pdf",
  "documentName": "important_document.pdf",
  "message": "Here's the PDF document you requested"
}
```

4. Send a document/PDF from S3:
```json
{
  "phone": "60123456789",
  "documentUrl": "https://bucket-name.s3.region.amazonaws.com/path/to/document.pdf",
  "documentName": "important_document.pdf",
  "message": "Here's the PDF document from S3"
}
```

**Response:**

Success:
```json
{
  "success": true,
  "message": "Message sent successfully",
  "data": {
    // Message details from WhatsApp
  }
}
```

Error:
```json
{
  "success": false,
  "message": "Error message"
}
```

### Check Connection Status

Check if the WhatsApp client is connected.

**Endpoint:** `GET /api/status`

**Response:**

```json
{
  "success": true,
  "connected": true,
  "qr": null
}
```

### Refresh QR Code

Refresh the QR code for WhatsApp Web connection.

**Endpoint:** `POST /api/refresh-qr`

**Response:**

```json
{
  "success": true,
  "message": "QR code refreshed"
}
```

### Logout

Logout from WhatsApp Web.

**Endpoint:** `POST /api/logout`

**Response:**

```json
{
  "success": true,
  "message": "Logged out successfully"
}
```

### Configure Webhook

Configure the webhook for receiving WhatsApp messages.

**Endpoint:** `POST /api/webhook/config`

**Request Body Parameters:**

| Parameter | Type    | Required | Description                                |
|-----------|---------|----------|--------------------------------------------|
| enabled   | boolean | Yes      | Whether to enable the webhook              |
| url       | string  | Yes*     | The URL to send webhook notifications to   |
| secret    | string  | No       | Secret key for webhook authentication      |

\* Required if `enabled` is `true`.

**Example Request:**

```json
{
  "enabled": true,
  "url": "https://example.com/webhook",
  "secret": "your-secret-key"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Webhook configuration updated"
}
```

### Test Webhook

Test the webhook configuration.

**Endpoint:** `POST /api/webhook/test`

**Response:**

```json
{
  "success": true,
  "message": "Test webhook sent successfully"
}
```

## Phone Number Format

The API will attempt to format phone numbers, but it's best to provide them in a consistent format:

- For Malaysian numbers: Use the format `60XXXXXXXXX` (without the leading 0)
- For other countries: Use the country code followed by the number without any special characters

## Error Handling

The API may return errors if:
1. WhatsApp is not connected
2. The phone number is invalid
3. The message is empty
4. There are connection issues with WhatsApp

## S3 URLs for Documents

When using S3 URLs with the `documentUrl` parameter, ensure that:

1. The S3 bucket has appropriate public access permissions
2. The S3 object (PDF file) is publicly readable
3. The URL format is correct (e.g., `https://bucket-name.s3.region.amazonaws.com/path/to/document.pdf`)

If the S3 object is not publicly accessible, you will receive a 403 Forbidden error.
