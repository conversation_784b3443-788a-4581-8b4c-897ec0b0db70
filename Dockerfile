FROM node:20-slim

# Set working directory
WORKDIR /app

# Set environment variables with defaults
ENV NODE_ENV=production \
    PORT=3030 \
    HOST="0.0.0.0" \
    WEBHOOK_URL="" \
    WEBHOOK_ENABLED="false" \
    WEBHOOK_SECRET="" \
    PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true \
    NODE_TLS_REJECT_UNAUTHORIZED=0

# Install system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    openssl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

RUN apt-get update && apt-get install -y iputils-ping curl

# Copy package files
COPY package*.json ./

# Install specific dependencies first to avoid issues
RUN npm install @adiwajshing/keyed-db@0.2.4

# Install a specific version of <PERSON><PERSON> that works well with Node.js 16
RUN npm install @whiskeysockets/baileys

# Install remaining Node.js dependencies
RUN npm install --production

# Copy project files
COPY . .

# Create whatsapp-session directory
RUN mkdir -p whatsapp-session

# Set Node.js options for compatibility
ENV NODE_OPTIONS="--no-warnings --max-http-header-size=16384 --max-old-space-size=4096"

# Expose port for the application
EXPOSE ${PORT}

# Command to run the application
CMD ["node", "whatsapp-web-server.js"]
