# WhatsApp API Deployment with Custom Domain 
 
## Prerequisites 
 
1. AWS account with permissions to create EC2 instances 
2. WhatsApp API Docker image already pushed to ECR 
3. Domain name (anchorsprint.com) with DNS access 
 
## Step 1: Launch an EC2 Instance 
 
1. Go to the AWS Management Console 
2. Navigate to EC2 service 
3. Click "Launch Instance" 
4. Choose Ubuntu Server 20.04 LTS 
5. Select an appropriate instance type (t2.small recommended) 
6. Configure instance details: 
   - Ensure "Auto-assign Public IP" is enabled 
   - Select an IAM role with ECR access permissions 
   - In "Advanced Details" section, copy the entire contents of user-data.sh 
7. Add storage (8GB minimum) 
8. Configure security group: 
   - HTTP (port 80) 
   - HTTPS (port 443) 
   - SSH (port 22) - restrict to your IP 
9. Launch the instance 
 
## Step 2: Configure DNS 
 
1. Get the public IP address of your EC2 instance 
2. Go to your domain registrar or DNS provider for anchorsprint.com 
3. Add an A record: 
   - Name: whatsapp (or @ for root domain) 
   - Value: [Your EC2 Public IP] 
   - TTL: 3600 (or lower like 300 for faster propagation) 
4. Wait for DNS propagation (can take up to 24-48 hours, but often much faster) 
 
## Step 3: Access Your WhatsApp API 
 
1. Wait 10-15 minutes for the installation to complete 
2. Access your WhatsApp API at: https://whatsapp.anchorsprint.com 
3. Scan the QR code with your WhatsApp mobile app to connect 
 
## Troubleshooting 
 
If you encounter issues: 
 
1. SSH into your EC2 instance: 
   ```bash 
   ssh -i your-key.pem ubuntu@[EC2-PUBLIC-IP] 
   ``` 
 
2. Check installation logs: 
   ```bash 
   sudo cat /var/log/cloud-init-output.log 
   ``` 
 
3. Check Docker container status: 
   ```bash 
   docker ps 
   docker logs $(docker ps -q --filter "name=whatsapp-api") 
   ``` 
 
4. Check Nginx configuration: 
   ```bash 
   sudo nginx -t 
   sudo systemctl status nginx 
   ``` 
 
5. Check SSL certificate: 
   ```bash 
   sudo certbot certificates 
   ``` 
