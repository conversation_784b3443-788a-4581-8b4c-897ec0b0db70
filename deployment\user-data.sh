#!/bin/bash 
# Update system packages 
apt-get update -y 
apt-get upgrade -y 
 
# Install Docker 
apt-get install -y apt-transport-https ca-certificates curl software-properties-common 
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | apt-key add - 
add-apt-repository "deb [arch=amd64] https://download.docker.com/linux/ubuntu \$(lsb_release -cs) stable" 
apt-get update -y 
apt-get install -y docker-ce docker-compose 
systemctl enable docker 
 
# Install Nginx for reverse proxy 
apt-get install -y nginx certbot python3-certbot-nginx 
 
# Install AWS CLI 
apt-get install -y unzip 
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" 
unzip awscliv2.zip 
./aws/install 
 
# Create directory for WhatsApp API 
mkdir -p /opt/whatsapp-api 
mkdir -p /opt/whatsapp-api/whatsapp-session 
 
# Create docker-compose.yml 
cat > /opt/whatsapp-api/docker-compose.yml << 'EOL' 
version: '3.8' 
 
services: 
  whatsapp-api: 
    ports: 
      - "127.0.0.1:3030:3030" 
    volumes: 
      - ./whatsapp-session:/app/whatsapp-session 
    restart: unless-stopped 
    environment: 
      - NODE_ENV=production 
      - PORT=3030 
      - HOST=0.0.0.0 
      - WEBHOOK_URL= 
      - WEBHOOK_ENABLED=false 
      - WEBHOOK_SECRET= 
EOL 
 
# Create Nginx configuration 
cat > /etc/nginx/sites-available/whatsapp.anchorsprint.com << 'EOL' 
server { 
    listen 80; 
    server_name whatsapp.anchorsprint.com; 
 
    location / { 
        proxy_pass http://127.0.0.1:3030; 
        proxy_http_version 1.1; 
        proxy_set_header Upgrade \$http_upgrade; 
        proxy_set_header Connection 'upgrade'; 
        proxy_set_header Host \$host; 
        proxy_cache_bypass \$http_upgrade; 
        proxy_read_timeout 86400s; 
        proxy_send_timeout 86400s; 
    } 
} 
EOL 
 
# Enable the Nginx site 
ln -s /etc/nginx/sites-available/whatsapp.anchorsprint.com /etc/nginx/sites-enabled/ 
nginx -t 
systemctl restart nginx 
 
# Create ECR login script 
cat > /opt/whatsapp-api/ecr-login.sh << 'EOL' 
#!/bin/bash 
EOL 
chmod +x /opt/whatsapp-api/ecr-login.sh 
 
# Create start script 
cat > /opt/whatsapp-api/start.sh << 'EOL' 
#!/bin/bash 
cd /opt/whatsapp-api 
./ecr-login.sh 
docker-compose pull 
docker-compose up -d 
EOL 
chmod +x /opt/whatsapp-api/start.sh 
 
# Create stop script 
cat > /opt/whatsapp-api/stop.sh << 'EOL' 
#!/bin/bash 
cd /opt/whatsapp-api 
docker-compose down 
EOL 
chmod +x /opt/whatsapp-api/stop.sh 
 
# Pull the Docker image and start the container 
cd /opt/whatsapp-api 
./ecr-login.sh 
docker-compose pull 
docker-compose up -d 
 
# Setup auto-start on boot 
cat > /etc/systemd/system/whatsapp-api.service << 'EOL' 
[Unit] 
Description=WhatsApp API Service 
After=docker.service 
Requires=docker.service 
 
[Service] 
Type=oneshot 
RemainAfterExit=yes 
WorkingDirectory=/opt/whatsapp-api 
ExecStart=/opt/whatsapp-api/start.sh 
ExecStop=/opt/whatsapp-api/stop.sh 
 
[Install] 
WantedBy=multi-user.target 
EOL 
 
systemctl enable whatsapp-api.service 
systemctl start whatsapp-api.service 
 
# Setup SSL with Let's Encrypt 
certbot --nginx -d whatsapp.anchorsprint.com --non-interactive --agree-tos --email <EMAIL> 
 
echo "WhatsApp API deployment complete. Access the web interface at https://whatsapp.anchorsprint.com" 
