// WebSocket connection manager
class WebSocketManager {
  constructor() {
    this.socket = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000;
    this.listeners = [];
    this.isConnected = false;
    this.currentQR = null;
    this.connectionStatus = 'disconnected';
    
    // Initialize connection
    this.connect();
    
    // Store instance in sessionStorage to persist across page navigation
    this.saveState();
    
    // Add event listener for beforeunload to save state
    window.addEventListener('beforeunload', () => {
      this.saveState();
    });
    
    // Load state from sessionStorage
    this.loadState();
  }
  
  // Connect to WebSocket
  connect() {
    if (this.socket && (this.socket.readyState === WebSocket.OPEN || this.socket.readyState === WebSocket.CONNECTING)) {
      console.log('WebSocket already connected or connecting');
      return;
    }
    
    // Connect to WebSocket
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}`;
    this.socket = new WebSocket(wsUrl);
    
    // Handle WebSocket connection open
    this.socket.onopen = () => {
      console.log('WebSocket connection established');
      this.reconnectAttempts = 0; // Reset reconnect attempts on successful connection
      this.isConnected = true;
      this.connectionStatus = 'connected';
      this.notifyListeners({ type: 'connection', connected: true });
      this.saveState();
    };
    
    // Handle WebSocket messages
    this.socket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('Received message:', data);
        
        if (data.type === 'qr') {
          this.currentQR = data.qr;
          this.connectionStatus = 'connecting';
        } else if (data.type === 'status') {
          this.isConnected = data.connected;
          this.connectionStatus = data.connected ? 'connected' : 'disconnected';
        }
        
        // Notify all listeners
        this.notifyListeners(data);
        this.saveState();
      } catch (error) {
        console.error('Error processing WebSocket message:', error);
      }
    };
    
    // Handle WebSocket errors
    this.socket.onerror = (event) => {
      console.error('WebSocket error:', event);
      this.connectionStatus = 'disconnected';
      this.notifyListeners({ type: 'connection', connected: false });
      this.saveState();
    };
    
    // Handle WebSocket connection close
    this.socket.onclose = () => {
      console.log('WebSocket connection closed');
      this.isConnected = false;
      this.connectionStatus = 'disconnected';
      this.notifyListeners({ type: 'connection', connected: false });
      this.saveState();
      
      // Try to reconnect after a delay if we haven't exceeded max attempts
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        this.reconnectAttempts++;
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        setTimeout(() => this.connect(), this.reconnectInterval);
      } else {
        console.log('Max reconnect attempts reached. Please refresh the page manually.');
      }
    };
  }
  
  // Add a listener for WebSocket events
  addListener(callback) {
    this.listeners.push(callback);
    
    // Immediately notify the new listener of the current state
    if (this.currentQR) {
      callback({ type: 'qr', qr: this.currentQR });
    }
    
    callback({ type: 'status', connected: this.isConnected });
    
    // Return a function to remove the listener
    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    };
  }
  
  // Notify all listeners of an event
  notifyListeners(data) {
    this.listeners.forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error('Error in WebSocket listener:', error);
      }
    });
  }
  
  // Save state to sessionStorage
  saveState() {
    const state = {
      isConnected: this.isConnected,
      currentQR: this.currentQR,
      connectionStatus: this.connectionStatus,
      reconnectAttempts: this.reconnectAttempts
    };
    
    sessionStorage.setItem('websocketState', JSON.stringify(state));
  }
  
  // Load state from sessionStorage
  loadState() {
    try {
      const stateJson = sessionStorage.getItem('websocketState');
      if (stateJson) {
        const state = JSON.parse(stateJson);
        this.isConnected = state.isConnected;
        this.currentQR = state.currentQR;
        this.connectionStatus = state.connectionStatus;
        this.reconnectAttempts = state.reconnectAttempts;
        
        // Notify listeners of the loaded state
        if (this.currentQR) {
          this.notifyListeners({ type: 'qr', qr: this.currentQR });
        }
        
        this.notifyListeners({ type: 'status', connected: this.isConnected });
      }
    } catch (error) {
      console.error('Error loading WebSocket state:', error);
    }
  }
  
  // Get the current connection status
  getStatus() {
    return {
      isConnected: this.isConnected,
      connectionStatus: this.connectionStatus,
      currentQR: this.currentQR
    };
  }
}

// Create a singleton instance
let websocketManager;

// Get the WebSocket manager instance
function getWebSocketManager() {
  if (!websocketManager) {
    websocketManager = new WebSocketManager();
  }
  return websocketManager;
}

// Export the WebSocket manager
window.WebSocketManager = {
  getInstance: getWebSocketManager
};
