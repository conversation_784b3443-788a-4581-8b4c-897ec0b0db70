import { Request, Response } from 'express';
import whatsappService from '../services/whatsapp.service';
import { SessionInfo } from '../types';

export const createSession = async (req: Request, res: Response) => {
  try {
    const { id, name } = req.body;

    if (!id) {
      return res.status(400).json({ success: false, message: 'Session ID is required' });
    }

    const session = await whatsappService.createSession(id, name);

    if (!session) {
      return res.status(500).json({ success: false, message: 'Failed to create session' });
    }

    console.log('Session created:', {
      id: session.id,
      name: session.name,
      ready: session.ready,
      hasQR: !!session.qr,
    });

    return res.status(201).json({
      success: true,
      data: {
        id: session.id,
        name: session.name,
        ready: session.ready,
        qr: session.qr,
      },
    });
  } catch (error) {
    console.error('Error creating session:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const getSession = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const session = whatsappService.getSession(id);

    if (!session) {
      return res.status(404).json({ success: false, message: 'Session not found' });
    }

    return res.status(200).json({
      success: true,
      data: {
        id: session.id,
        name: session.name,
        ready: session.ready,
        qr: session.qr,
        lastSeen: session.lastSeen,
      },
    });
  } catch (error) {
    console.error('Error getting session:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const getAllSessions = async (req: Request, res: Response) => {
  try {
    const sessions = whatsappService.getAllSessions();

    const sessionInfos: SessionInfo[] = sessions.map(session => ({
      id: session.id,
      name: session.name,
      ready: session.ready,
      lastSeen: session.lastSeen,
    }));

    return res.status(200).json({
      success: true,
      data: sessionInfos,
    });
  } catch (error) {
    console.error('Error getting all sessions:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};

export const deleteSession = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const success = await whatsappService.deleteSession(id);

    if (!success) {
      return res.status(404).json({ success: false, message: 'Session not found or could not be deleted' });
    }

    return res.status(200).json({
      success: true,
      message: 'Session deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting session:', error);
    return res.status(500).json({ success: false, message: 'Internal server error' });
  }
};
