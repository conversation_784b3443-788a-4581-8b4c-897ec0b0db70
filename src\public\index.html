<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>WhatsApp API Portal</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      padding-top: 2rem;
      background-color: #f8f9fa;
    }
    .card {
      margin-bottom: 1.5rem;
      box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }
    .qr-container {
      text-align: center;
      padding: 1rem;
      background-color: white;
      border-radius: 0.25rem;
      margin-top: 1rem;
    }
    .session-list {
      max-height: 400px;
      overflow-y: auto;
    }
    .status-badge {
      font-size: 0.8rem;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="row justify-content-center">
      <div class="col-md-10">
        <div class="card">
          <div class="card-header bg-primary text-white">
            <h4 class="mb-0">WhatsApp API Portal</h4>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <h5>Create New Session</h5>
                <form id="createSessionForm">
                  <div class="mb-3">
                    <label for="sessionId" class="form-label">Session ID</label>
                    <input type="text" class="form-control" id="sessionId" required>
                    <div class="form-text">A unique identifier for this WhatsApp session</div>
                  </div>
                  <div class="mb-3">
                    <label for="sessionName" class="form-label">Session Name (Optional)</label>
                    <input type="text" class="form-control" id="sessionName">
                  </div>
                  <button type="submit" class="btn btn-primary">Create Session</button>
                </form>

                <div id="qrContainer" class="qr-container">
                  <h5>Scan this QR code with WhatsApp</h5>
                  <div id="qrCode">
                    <!-- Fallback QR code image -->
                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=https://example.com/whatsapp-test" alt="WhatsApp QR Code" width="200" height="200">
                  </div>
                  <div class="mt-2">
                    <small class="text-muted">Open WhatsApp on your phone, tap Menu or Settings and select WhatsApp Web</small>
                  </div>
                </div>
              </div>

              <div class="col-md-6">
                <h5>Active Sessions</h5>
                <div class="session-list" id="sessionList">
                  <div class="text-center py-4 text-muted">
                    <p>No active sessions</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Session Template -->
  <template id="sessionTemplate">
    <div class="card mb-2 session-item">
      <div class="card-body py-2">
        <div class="d-flex justify-content-between align-items-center">
          <div>
            <h6 class="mb-0 session-name">Session Name</h6>
            <small class="text-muted session-id">ID: session123</small>
          </div>
          <div class="d-flex align-items-center">
            <span class="badge rounded-pill me-2 status-badge">Status</span>
            <button class="btn btn-sm btn-danger delete-btn">Delete</button>
          </div>
        </div>
      </div>
    </div>
  </template>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <!-- QR Code libraries - include both for compatibility -->
  <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.0/build/qrcode.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"></script>
  <script src="js/main.js"></script>
</body>
</html>
