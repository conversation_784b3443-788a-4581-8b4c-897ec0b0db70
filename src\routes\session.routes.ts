import { Router } from 'express';
import * as sessionController from '../controllers/session.controller';

const router = Router();

// Create a new WhatsApp session
router.post('/', sessionController.createSession as any);

// Get all sessions
router.get('/', sessionController.getAllSessions as any);

// Get a specific session
router.get('/:id', sessionController.getSession as any);

// Delete a session
router.delete('/:id', sessionController.deleteSession as any);

export default router;
