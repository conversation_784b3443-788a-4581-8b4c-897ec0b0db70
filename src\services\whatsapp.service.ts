import makeWASocket, {
  DisconnectReason,
  useMultiFileAuthState,
  WASocket,
  fetchLatestBaileysVersion,
  makeCacheableSignalKeyStore,
  isJidUser,
} from '@whiskeysockets/baileys';
import { Boom } from '@hapi/boom';
// @ts-ignore
import qrcode from 'qrcode-terminal';
import fs from 'fs';
import path from 'path';
import { WhatsAppSession, WebhookPayload } from '../types';
import config from '../config';
import { sendWebhook } from '../utils/webhook';

class WhatsAppService {
  private sessions: Map<string, WhatsAppSession> = new Map();

  constructor() {
    // Create sessions directory if it doesn't exist
    if (!fs.existsSync(config.sessionsDir)) {
      fs.mkdirSync(config.sessionsDir, { recursive: true });
    }
  }

  public async createSession(sessionId: string, sessionName?: string): Promise<WhatsAppSession | null> {
    try {
      // Check if session already exists
      if (this.sessions.has(sessionId)) {
        return this.sessions.get(sessionId) || null;
      }

      // Create session directory
      const sessionDir = path.join(config.sessionsDir, sessionId);
      if (!fs.existsSync(sessionDir)) {
        fs.mkdirSync(sessionDir, { recursive: true });
      }

      // Initialize auth state
      const { state, saveCreds } = await useMultiFileAuthState(sessionDir);

      // Fetch latest version
      const { version } = await fetchLatestBaileysVersion();

      // Create WhatsApp client with exact settings from test script
      const sock = makeWASocket({
        auth: {
          creds: state.creds,
          // @ts-ignore
          keys: makeCacheableSignalKeyStore(state.keys, console.log),
        },
        printQRInTerminal: true, // Enable QR code in terminal for debugging
        browser: ['WhatsApp API Test', 'Chrome', '103.0.5060.114'], // Exact browser signature from test script
      });

      // Create session object without a QR code initially
      // The QR code will be set when Baileys generates it
      const session: WhatsAppSession = {
        id: sessionId,
        name: sessionName,
        client: sock,
        ready: false,
      };

      // Store session immediately
      this.sessions.set(sessionId, session);

      // Set up connection events
      sock.ev.on('connection.update', async (update) => {
        const { connection, lastDisconnect, qr } = update;

        if (qr) {
          console.log(`QR code received for session ${sessionId}`);

          // Store the QR code in the session
          session.qr = qr;

          // Display QR code in terminal for debugging
          qrcode.generate(qr, { small: true });

          // Log the QR code for debugging
          console.log(`Raw QR code data for session ${sessionId}:`, qr);

          // Send webhook notification
          await sendWebhook({
            sessionId,
            event: 'qr',
            data: { qr },
          });

          // Update the session in the map to ensure QR code is available
          this.sessions.set(sessionId, session);

          // Log confirmation that session was updated
          console.log(`Session ${sessionId} updated with QR code`);

          // Log instructions for debugging
          console.log('To scan the QR code:');
          console.log('1. Open WhatsApp on your phone');
          console.log('2. Tap Menu or Settings and select WhatsApp Web/Desktop');
          console.log('3. Point your phone at this screen to capture the QR code');
        }

        if (connection === 'close') {
          const shouldReconnect = (lastDisconnect?.error as Boom)?.output?.statusCode !== DisconnectReason.loggedOut;

          if (shouldReconnect) {
            // Reconnect if not logged out
            await this.createSession(sessionId, sessionName);
          } else {
            // Remove session if logged out
            this.sessions.delete(sessionId);

            // Send webhook notification
            await sendWebhook({
              sessionId,
              event: 'disconnected',
              data: { reason: 'logged_out' },
            });
          }
        } else if (connection === 'open') {
          // Session is ready
          session.ready = true;
          session.lastSeen = new Date();

          // Send webhook notification
          await sendWebhook({
            sessionId,
            event: 'connected',
            data: {
              user: sock.user,
              time: new Date().toISOString(),
            },
          });
        }
      });

      // Save credentials on update
      sock.ev.on('creds.update', saveCreds);

      // Handle messages
      sock.ev.on('messages.upsert', async (m) => {
        if (m.type === 'notify') {
          for (const msg of m.messages) {
            if (!msg.key.fromMe && isJidUser(msg.key.remoteJid || '')) {
              // Send webhook notification for new message
              await sendWebhook({
                sessionId,
                event: 'message',
                data: msg,
              });
            }
          }
        }
      });

      // Store session
      this.sessions.set(sessionId, session);
      return session;
    } catch (error) {
      console.error(`Error creating session ${sessionId}:`, error);
      return null;
    }
  }

  public getSession(sessionId: string): WhatsAppSession | null {
    return this.sessions.get(sessionId) || null;
  }

  public getAllSessions(): WhatsAppSession[] {
    return Array.from(this.sessions.values());
  }

  public async deleteSession(sessionId: string): Promise<boolean> {
    try {
      const session = this.sessions.get(sessionId);
      if (!session) {
        return false;
      }

      // Close connection
      session.client.end(undefined);

      // Remove from sessions map
      this.sessions.delete(sessionId);

      // Delete session directory
      const sessionDir = path.join(config.sessionsDir, sessionId);
      if (fs.existsSync(sessionDir)) {
        fs.rmSync(sessionDir, { recursive: true, force: true });
      }

      return true;
    } catch (error) {
      console.error(`Error deleting session ${sessionId}:`, error);
      return false;
    }
  }

  public async sendTextMessage(sessionId: string, to: string, text: string): Promise<any> {
    try {
      const session = this.sessions.get(sessionId);
      if (!session || !session.ready) {
        throw new Error('Session not found or not ready');
      }

      // Format phone number
      const formattedNumber = this.formatPhoneNumber(to);

      // Send message
      const result = await session.client.sendMessage(formattedNumber, { text });
      return result;
    } catch (error) {
      console.error(`Error sending message in session ${sessionId}:`, error);
      throw error;
    }
  }

  public async sendMediaMessage(
    sessionId: string,
    to: string,
    url: string,
    caption?: string,
    mimetype?: string
  ): Promise<any> {
    try {
      const session = this.sessions.get(sessionId);
      if (!session || !session.ready) {
        throw new Error('Session not found or not ready');
      }

      // Format phone number
      const formattedNumber = this.formatPhoneNumber(to);

      // Send media message
      const result = await session.client.sendMessage(formattedNumber, {
        image: { url },
        caption: caption || '',
        mimetype: mimetype || 'image/jpeg',
      });

      return result;
    } catch (error) {
      console.error(`Error sending media message in session ${sessionId}:`, error);
      throw error;
    }
  }

  private formatPhoneNumber(phoneNumber: string): string {
    // Remove any non-digit characters
    const digits = phoneNumber.replace(/\D/g, '');

    // Ensure the number has the country code
    if (digits.startsWith('0')) {
      return `6${digits.substring(1)}@s.whatsapp.net`;
    } else if (!digits.includes('@')) {
      return `${digits}@s.whatsapp.net`;
    }

    return phoneNumber;
  }
}

// Create singleton instance
const whatsappService = new WhatsAppService();
export default whatsappService;
