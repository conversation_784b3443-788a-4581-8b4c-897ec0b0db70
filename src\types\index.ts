import { WASocket } from '@whiskeysockets/baileys';

export interface WhatsAppSession {
  id: string;
  name?: string;
  client: WASocket;
  qr?: string;
  ready: boolean;
  lastSeen?: Date;
}

export interface WebhookPayload {
  sessionId: string;
  event: string;
  data: any;
}

export interface SendMessageRequest {
  to: string;
  text?: string;
  media?: {
    url: string;
    caption?: string;
    mimetype?: string;
  };
}

export interface SessionInfo {
  id: string;
  name?: string;
  ready: boolean;
  lastSeen?: Date;
}
