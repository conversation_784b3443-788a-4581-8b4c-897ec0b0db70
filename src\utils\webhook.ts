import axios from 'axios';
import config from '../config';
import { WebhookPayload } from '../types';

export async function sendWebhook(payload: WebhookPayload): Promise<boolean> {
  try {
    if (!config.webhookUrl || config.webhookUrl.trim() === '') {
      console.log('No webhook URL configured, skipping webhook notification');
      return false;
    }

    await axios.post(config.webhookUrl, payload, {
      headers: {
        'Content-Type': 'application/json',
      },
    });

    return true;
  } catch (error) {
    console.error('Error sending webhook notification:', error);
    return false;
  }
}

export default sendWebhook;
